{"environment_id": "apartment_01", "last_updated": "2025-01-15T20:00:00Z", "scenes": [{"scene_id": "scene_kitchen_01", "scene_class": "Kitchen", "bounding_box_meters": {"min_corner": {"x": 0.0, "y": 0.0, "z": 0.0}, "max_corner": {"x": 5.0, "y": 4.0, "z": 2.8}}, "last_visited_timestamp": "2025-01-15T19:45:30Z", "static_objects": [{"object_id": "stove_001", "object_class": "<PERSON><PERSON>", "position_meters": {"x": 1.0, "y": 1.0, "z": 0.9}, "dimensions_meters": {"length": 0.6, "width": 0.5, "height": 0.1}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [200, 200, 200], "surface_state_class": "dirty", "detection_confidence": 0.92, "last_seen_timestamp": "2025-01-15T19:45:25Z"}, "physical": {"burner_states": ["off", "off", "off", "off"], "temperature_celsius": 25.0, "material_class": "stainless_steel"}}}, {"object_id": "refrigerator_001", "object_class": "Refrigerator", "position_meters": {"x": 0.5, "y": 3.5, "z": 0.0}, "dimensions_meters": {"length": 0.7, "width": 0.6, "height": 1.8}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [255, 255, 255], "surface_state_class": "clean", "detection_confidence": 0.95, "last_seen_timestamp": "2025-01-15T19:45:20Z"}, "physical": {"door_state_class": "closed", "internal_temperature_celsius": 4.0, "material_class": "metal"}}}, {"object_id": "sink_kitchen_001", "object_class": "Sink", "position_meters": {"x": 2.5, "y": 0.5, "z": 0.9}, "dimensions_meters": {"length": 0.8, "width": 0.5, "height": 0.2}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [250, 250, 250], "surface_state_class": "wet", "detection_confidence": 0.9, "last_seen_timestamp": "2025-01-15T19:45:28Z"}, "physical": {"faucet_state_class": "off", "water_level_percent": 10, "material_class": "ceramic"}}}, {"object_id": "dishwasher_001", "object_class": "Dishwasher", "position_meters": {"x": 3.5, "y": 0.5, "z": 0.0}, "dimensions_meters": {"length": 0.6, "width": 0.5, "height": 0.8}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [240, 240, 240], "surface_state_class": "clean", "detection_confidence": 0.88, "last_seen_timestamp": "2025-01-15T19:45:15Z"}, "physical": {"door_state_class": "closed", "operation_state_class": "idle", "material_class": "metal"}}}, {"object_id": "microwave_001", "object_class": "Microwave", "position_meters": {"x": 4.0, "y": 1.5, "z": 1.2}, "dimensions_meters": {"length": 0.5, "width": 0.4, "height": 0.3}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [220, 220, 220], "surface_state_class": "clean", "detection_confidence": 0.93, "last_seen_timestamp": "2025-01-15T19:45:22Z"}, "physical": {"door_state_class": "closed", "operation_state_class": "off", "material_class": "plastic"}}}, {"object_id": "cutting_board_001", "object_class": "CuttingBoard", "position_meters": {"x": 2.0, "y": 1.5, "z": 0.9}, "dimensions_meters": {"length": 0.4, "width": 0.3, "height": 0.02}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [180, 150, 120], "surface_state_class": "dirty", "detection_confidence": 0.85, "last_seen_timestamp": "2025-01-15T19:45:18Z"}, "physical": {"material_class": "wood", "cleanliness_level": 0.3}}}, {"object_id": "knife_001", "object_class": "Knife", "position_meters": {"x": 2.1, "y": 1.6, "z": 0.92}, "dimensions_meters": {"length": 0.25, "width": 0.03, "height": 0.02}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0.707, "w": 0.707}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [200, 200, 200], "surface_state_class": "clean", "detection_confidence": 0.9, "last_seen_timestamp": "2025-01-15T19:45:19Z"}, "physical": {"material_class": "steel", "sharpness_level": 0.8}}}, {"object_id": "coffee_maker_001", "object_class": "CoffeeMaker", "position_meters": {"x": 3.0, "y": 2.0, "z": 0.9}, "dimensions_meters": {"length": 0.3, "width": 0.2, "height": 0.3}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [100, 100, 100], "surface_state_class": "clean", "detection_confidence": 0.87, "last_seen_timestamp": "2025-01-15T19:45:12Z"}, "physical": {"operation_state_class": "off", "water_level_percent": 80, "material_class": "plastic"}}}], "dynamic_events": [{"event_id": "evt_sound_kitchen_001", "event_class": "transient_sound", "timestamp": "2025-01-15T19:45:30Z", "duration_seconds": 1.2, "estimated_source_position": {"x": 2.0, "y": 1.5, "z": 1.0}, "sensor_readings": {"audio": {"peak_decibels": -30.0, "average_decibels": -45.0, "dominant_frequencies_hz": [200, 400, 600], "sound_class": "chopping", "classification_confidence": 0.88}}}, {"event_id": "evt_heat_kitchen_001", "event_class": "temperature_change", "timestamp": "2025-01-15T19:45:25Z", "duration_seconds": 5.0, "estimated_source_position": {"x": 1.0, "y": 1.0, "z": 1.0}, "sensor_readings": {"thermal": {"temperature_change_celsius": 2.5, "heat_source_class": "stove_burner", "detection_confidence": 0.82}}}], "ambient_readings": {"temperature_celsius": 24.0, "humidity_percent": 55.0, "light_level_lux": 450, "background_noise_decibels": -60.0}}]}