{"environment_id": "apartment_01", "last_updated": "2025-01-15T20:00:00Z", "scenes": [{"scene_id": "scene_bathroom_01", "scene_class": "Bathroom", "bounding_box_meters": {"min_corner": {"x": 0.0, "y": 0.0, "z": 0.0}, "max_corner": {"x": 4.0, "y": 3.0, "z": 2.5}}, "last_visited_timestamp": "2025-01-15T19:55:10Z", "static_objects": [{"object_id": "sink_001", "object_class": "Sink", "position_meters": {"x": 2.5, "y": 1.0, "z": 0.8}, "dimensions_meters": {"length": 0.6, "width": 0.45, "height": 0.2}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [250, 250, 250], "surface_state_class": "moderate", "detection_confidence": 0.95, "last_seen_timestamp": "2025-01-15T19:55:08Z"}, "physical": {"faucet_state_class": "off", "material_class": "ceramic"}}}, {"object_id": "toilet_001", "object_class": "<PERSON><PERSON><PERSON>", "position_meters": {"x": 1.0, "y": 2.0, "z": 0.4}, "dimensions_meters": {"length": 0.7, "width": 0.4, "height": 0.8}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0.707, "w": 0.707}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [248, 248, 248], "surface_state_class": "dirty", "detection_confidence": 0.88, "last_seen_timestamp": "2025-01-15T19:54:50Z"}, "physical": {"lid_state_class": "open", "material_class": "porcelain"}}}, {"object_id": "sponge_001", "object_class": "Sponge", "position_meters": {"x": 2.3, "y": 0.8, "z": 0.9}, "dimensions_meters": {"length": 0.12, "width": 0.08, "height": 0.04}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [200, 200, 200], "surface_state_class": "clean", "detection_confidence": 0.92, "last_seen_timestamp": "2025-01-15T19:55:05Z"}, "physical": {"material_class": "cellulose", "wetness_level": 0.3}}}, {"object_id": "bathtub_001", "object_class": "Bathtub", "position_meters": {"x": 3.0, "y": 2.5, "z": 0.2}, "dimensions_meters": {"length": 1.5, "width": 0.7, "height": 0.5}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0.707, "w": 0.707}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [245, 245, 245], "surface_state_class": "stained", "detection_confidence": 0.9, "last_seen_timestamp": "2025-01-15T19:55:02Z"}, "physical": {"fill_level_percent": 5, "drain_state_class": "closed", "material_class": "acrylic"}}}, {"object_id": "mirror_001", "object_class": "Mirror", "position_meters": {"x": 2.5, "y": 0.0, "z": 1.2}, "dimensions_meters": {"length": 0.8, "width": 0.05, "height": 1.0}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [255, 255, 255], "surface_state_class": "clean", "detection_confidence": 0.98, "last_seen_timestamp": "2025-01-15T19:55:12Z"}, "physical": {"material_class": "glass", "reflection_quality": 0.95}}}, {"object_id": "towel_rack_001", "object_class": "TowelRack", "position_meters": {"x": 0.5, "y": 1.0, "z": 1.5}, "dimensions_meters": {"length": 0.6, "width": 0.1, "height": 0.3}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [180, 180, 180], "surface_state_class": "clean", "detection_confidence": 0.9, "last_seen_timestamp": "2025-01-15T19:55:15Z"}, "physical": {"material_class": "metal", "towel_count": 2}}}], "dynamic_events": [{"event_id": "evt_sound_12345", "event_class": "transient_sound", "timestamp": "2025-01-15T19:55:07Z", "duration_seconds": 2.5, "estimated_source_position": {"x": 2.5, "y": 1.0, "z": 1.0}, "sensor_readings": {"audio": {"peak_decibels": -25.0, "average_decibels": -40.0, "dominant_frequencies_hz": [400, 800, 1200], "sound_class": "water_running", "classification_confidence": 0.85}}}, {"event_id": "evt_motion_12346", "event_class": "motion_detected", "timestamp": "2025-01-15T19:55:20Z", "duration_seconds": 0.5, "estimated_source_position": {"x": 2.0, "y": 1.5, "z": 1.0}, "sensor_readings": {"visual": {"motion_velocity_mps": 0.8, "motion_direction_degrees": 45, "detection_confidence": 0.75}}}], "ambient_readings": {"temperature_celsius": 23.5, "humidity_percent": 65.0, "light_level_lux": 300, "background_noise_decibels": -65.0}}]}