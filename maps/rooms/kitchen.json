{"scene_id": "kitchen_001", "scene_name": "厨房", "objects": [{"id": "sink_001", "class_name": "sink", "position": {"x": 1.2, "y": 0.0, "z": 0.9}, "state": "dirty", "is_movable": false, "is_interactable": true, "properties": [{"type": "material", "value": "stainless_steel"}], "associated_tools": ["sponge_001", "dish_soap_001"]}, {"id": "faucet_001", "class_name": "faucet", "position": {"x": 1.2, "y": 0.0, "z": 1.0}, "state": "off", "is_movable": false, "is_interactable": true, "properties": [{"type": "material", "value": "stainless_steel"}], "associated_tools": []}, {"id": "dish_rack_001", "class_name": "dish_rack", "position": {"x": 1.35, "y": 0.0, "z": 1.0}, "state": "available", "is_movable": true, "is_interactable": true, "properties": [{"type": "capacity", "value": "12_plates"}], "associated_tools": []}, {"id": "stove_001", "class_name": "stove", "position": {"x": 0.6, "y": 0.0, "z": 0.9}, "state": "off", "is_movable": false, "is_interactable": true, "properties": [{"type": "burners", "value": 4}], "associated_tools": ["cleaner_foam_001"]}, {"id": "range_hood_001", "class_name": "range_hood", "position": {"x": 0.6, "y": 0.0, "z": 1.6}, "state": "off", "is_movable": false, "is_interactable": true, "properties": [{"type": "filter", "value": "mesh"}], "associated_tools": ["degreaser_001"]}, {"id": "microwave_001", "class_name": "microwave", "position": {"x": 0.2, "y": 0.0, "z": 1.0}, "state": "off", "is_movable": true, "is_interactable": true, "properties": [{"type": "power", "value": "800W"}], "associated_tools": []}, {"id": "fridge_001", "class_name": "refrigerator", "position": {"x": 2.0, "y": 0.0, "z": 0.0}, "state": "closed", "is_movable": false, "is_interactable": true, "properties": [{"type": "doors", "value": 2}], "associated_tools": []}, {"id": "cabinet_001", "class_name": "cabinet", "position": {"x": 0.0, "y": 0.0, "z": 1.0}, "state": "closed", "is_movable": false, "is_interactable": true, "properties": [{"type": "shelves", "value": 3}], "associated_tools": []}, {"id": "drawer_001", "class_name": "drawer", "position": {"x": 0.1, "y": 0.0, "z": 0.8}, "state": "closed", "is_movable": false, "is_interactable": true, "properties": [{"type": "compartments", "value": 4}], "associated_tools": []}, {"id": "cutting_board_001", "class_name": "cutting_board", "position": {"x": 0.4, "y": 0.0, "z": 0.95}, "state": "available", "is_movable": true, "is_interactable": true, "properties": [{"type": "material", "value": "wood"}], "associated_tools": []}, {"id": "knife_001", "class_name": "knife", "position": {"x": 0.45, "y": 0.0, "z": 0.95}, "state": "available", "is_movable": true, "is_interactable": true, "properties": [{"type": "type", "value": "chef"}], "associated_tools": ["knife_block_001"]}, {"id": "pot_001", "class_name": "pot", "position": {"x": 0.55, "y": 0.0, "z": 0.95}, "state": "available", "is_movable": true, "is_interactable": true, "properties": [{"type": "capacity", "value": "3L"}], "associated_tools": ["lid_001"]}, {"id": "pan_001", "class_name": "pan", "position": {"x": 0.58, "y": 0.0, "z": 0.95}, "state": "available", "is_movable": true, "is_interactable": true, "properties": [{"type": "diameter", "value": "28cm"}], "associated_tools": ["spatula_001"]}, {"id": "trash_bin_001", "class_name": "trash_bin", "position": {"x": 0.3, "y": 0.0, "z": 0.0}, "state": "half_full", "is_movable": true, "is_interactable": true, "properties": [{"type": "material", "value": "plastic"}], "associated_tools": ["trash_bag_001"]}, {"id": "broom_001", "class_name": "broom", "position": {"x": 2.2, "y": 0.0, "z": 0.0}, "state": "available", "is_movable": true, "is_interactable": true, "properties": [{"type": "material", "value": "plastic"}], "associated_tools": ["dustpan_001"]}, {"id": "mop_001", "class_name": "mop", "position": {"x": 2.25, "y": 0.0, "z": 0.0}, "state": "clean", "is_movable": true, "is_interactable": true, "properties": [{"type": "type", "value": "sponge_mop"}], "associated_tools": ["bucket_001"]}], "areas": [{"id": "counter_area_001", "name": "台面区域", "boundary": {"min_x": 0.0, "max_x": 1.5, "min_y": 0.0, "max_y": 1.0, "min_z": 0.8, "max_z": 1.2}}, {"id": "stove_area_001", "name": "灶台区域", "boundary": {"min_x": 0.4, "max_x": 0.8, "min_y": 0.0, "max_y": 1.2, "min_z": 0.8, "max_z": 1.6}}, {"id": "sink_area_001", "name": "水槽区域", "boundary": {"min_x": 1.0, "max_x": 1.4, "min_y": 0.0, "max_y": 1.2, "min_z": 0.8, "max_z": 1.2}}, {"id": "floor_area_002", "name": "地板区域", "boundary": {"min_x": 0.0, "max_x": 2.5, "min_y": 0.0, "max_y": 0.0, "min_z": 0.0, "max_z": 2.0}}], "relationships": [{"subject_id": "faucet_001", "relationship_type": "on", "object_id": "sink_001"}, {"subject_id": "dish_rack_001", "relationship_type": "near", "object_id": "sink_001"}, {"subject_id": "range_hood_001", "relationship_type": "above", "object_id": "stove_001"}, {"subject_id": "knife_001", "relationship_type": "on", "object_id": "cutting_board_001"}, {"subject_id": "pot_001", "relationship_type": "on", "object_id": "stove_001"}, {"subject_id": "pan_001", "relationship_type": "on", "object_id": "stove_001"}, {"subject_id": "trash_bin_001", "relationship_type": "on", "object_id": "floor_area_002"}]}