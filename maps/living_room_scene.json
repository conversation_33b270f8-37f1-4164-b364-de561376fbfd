{"environment_id": "apartment_01", "last_updated": "2025-01-15T20:00:00Z", "scenes": [{"scene_id": "scene_living_room_01", "scene_class": "LivingRoom", "bounding_box_meters": {"min_corner": {"x": 0.0, "y": 0.0, "z": 0.0}, "max_corner": {"x": 6.0, "y": 5.0, "z": 3.0}}, "last_visited_timestamp": "2025-01-15T19:50:15Z", "static_objects": [{"object_id": "sofa_001", "object_class": "So<PERSON>", "position_meters": {"x": 2.0, "y": 2.0, "z": 0.0}, "dimensions_meters": {"length": 2.0, "width": 0.8, "height": 0.4}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [120, 80, 60], "surface_state_class": "clean", "detection_confidence": 0.95, "last_seen_timestamp": "2025-01-15T19:50:10Z"}, "physical": {"material_class": "fabric", "cushion_count": 3, "occupancy_state": "empty"}}}, {"object_id": "tv_001", "object_class": "Television", "position_meters": {"x": 3.0, "y": 0.5, "z": 1.2}, "dimensions_meters": {"length": 1.2, "width": 0.1, "height": 0.7}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [50, 50, 50], "surface_state_class": "clean", "detection_confidence": 0.98, "last_seen_timestamp": "2025-01-15T19:50:12Z"}, "physical": {"power_state_class": "on", "brightness_level": 0.7, "material_class": "plastic"}}}, {"object_id": "coffee_table_001", "object_class": "CoffeeTable", "position_meters": {"x": 2.5, "y": 2.5, "z": 0.0}, "dimensions_meters": {"length": 1.0, "width": 0.6, "height": 0.4}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [200, 180, 160], "surface_state_class": "cluttered", "detection_confidence": 0.9, "last_seen_timestamp": "2025-01-15T19:50:08Z"}, "physical": {"material_class": "wood", "object_count_on_surface": 4}}}, {"object_id": "lamp_001", "object_class": "<PERSON><PERSON>", "position_meters": {"x": 1.0, "y": 1.0, "z": 0.0}, "dimensions_meters": {"length": 0.2, "width": 0.2, "height": 1.5}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [255, 255, 200], "surface_state_class": "clean", "detection_confidence": 0.92, "last_seen_timestamp": "2025-01-15T19:50:05Z"}, "physical": {"power_state_class": "on", "brightness_level": 0.6, "material_class": "metal"}}}, {"object_id": "bookshelf_001", "object_class": "Bookshelf", "position_meters": {"x": 5.5, "y": 1.0, "z": 0.0}, "dimensions_meters": {"length": 0.3, "width": 1.0, "height": 2.0}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [150, 100, 80], "surface_state_class": "organized", "detection_confidence": 0.88, "last_seen_timestamp": "2025-01-15T19:50:03Z"}, "physical": {"material_class": "wood", "book_count": 25, "shelf_count": 4}}}, {"object_id": "remote_control_001", "object_class": "RemoteControl", "position_meters": {"x": 2.6, "y": 2.6, "z": 0.4}, "dimensions_meters": {"length": 0.15, "width": 0.05, "height": 0.03}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [100, 100, 100], "surface_state_class": "clean", "detection_confidence": 0.85, "last_seen_timestamp": "2025-01-15T19:50:14Z"}, "physical": {"material_class": "plastic", "battery_level_percent": 75}}}, {"object_id": "plant_001", "object_class": "Plant", "position_meters": {"x": 4.5, "y": 4.0, "z": 0.0}, "dimensions_meters": {"length": 0.4, "width": 0.4, "height": 1.2}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [50, 150, 50], "surface_state_class": "healthy", "detection_confidence": 0.9, "last_seen_timestamp": "2025-01-15T19:50:01Z"}, "physical": {"material_class": "organic", "soil_moisture_percent": 60, "plant_health_level": 0.8}}}, {"object_id": "window_001", "object_class": "Window", "position_meters": {"x": 0.0, "y": 2.5, "z": 1.0}, "dimensions_meters": {"length": 0.1, "width": 2.0, "height": 1.5}, "orientation_quaternion": {"x": 0, "y": 0, "z": 0, "w": 1}, "is_occluded": false, "sensor_readings": {"visual": {"primary_color_rgb": [200, 200, 255], "surface_state_class": "clean", "detection_confidence": 0.95, "last_seen_timestamp": "2025-01-15T19:50:00Z"}, "physical": {"material_class": "glass", "curtain_state_class": "open", "light_transmission": 0.8}}}], "dynamic_events": [{"event_id": "evt_sound_living_001", "event_class": "transient_sound", "timestamp": "2025-01-15T19:50:15Z", "duration_seconds": 3.0, "estimated_source_position": {"x": 3.0, "y": 0.5, "z": 1.5}, "sensor_readings": {"audio": {"peak_decibels": -20.0, "average_decibels": -35.0, "dominant_frequencies_hz": [100, 200, 500, 1000], "sound_class": "tv_audio", "classification_confidence": 0.92}}}, {"event_id": "evt_motion_living_001", "event_class": "motion_detected", "timestamp": "2025-01-15T19:50:12Z", "duration_seconds": 1.0, "estimated_source_position": {"x": 2.0, "y": 2.0, "z": 0.5}, "sensor_readings": {"visual": {"motion_velocity_mps": 0.3, "motion_direction_degrees": 90, "detection_confidence": 0.78}}}], "ambient_readings": {"temperature_celsius": 22.0, "humidity_percent": 45.0, "light_level_lux": 350, "background_noise_decibels": -55.0}}]}