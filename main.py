#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三个LLM协作的具身任务规划系统 V2
LLMA: 世界场景描述LLM
LLMB: 任务思考LLM  
LLMC: 任务序列优化LLM
"""

import dashscope
from dashscope import Generation
import json
import time
from typing import Dict, List, Any, Optional
from config import DASHSCOPE_API_KEY
from prompts import (
    LLMA_SYSTEM_PROMPT,
    LLMA_ANALYSIS_PROMPT,
    LLMB_SYSTEM_PROMPT,
    LLMC_SYSTEM_PROMPT,
    CONVERSATION_FLOW_PROMPTS,
    TASK_EXPANDER_SYSTEM_PROMPT,
    TASK_EXPANDER_PROMPT,
)
from mab import MABBalancer

class LLMBase:
    """LLM基础类"""
    
    def __init__(self, api_key: str, system_prompt: str, name: str):
        self.api_key = api_key
        self.system_prompt = system_prompt
        self.name = name
        self.setup_api()
    
    def setup_api(self):
        """设置API"""
        dashscope.api_key = self.api_key
        print(f"✅ {self.name} API密钥已设置")
    
    def call_llm(self, prompt: str, max_tokens: int = 1500, max_retries: int = 3) -> str:
        """调用LLM，带重试机制"""
        for attempt in range(max_retries):
            try:
                # 构建完整的提示词
                full_prompt = f"{self.system_prompt}\n\n{prompt}"
                
                response = Generation.call(
                    model="deepseek-v3.1",
                    prompt=full_prompt,
                    result_format='message',
                    max_tokens=max_tokens,
                    temperature=0.7,
                    top_p=0.8,
                )
                
                if response.status_code == 200:
                    return response.output.choices[0].message.content
                else:
                    print(f"⚠️ {self.name} 第{attempt + 1}次尝试失败: {response.message}")
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 2
                        print(f"⏳ 等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        return self._get_fallback_response(prompt)
                        
            except Exception as e:
                error_msg = str(e)
                print(f"⚠️ {self.name} 第{attempt + 1}次尝试失败: {error_msg}")
                
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    return self._get_fallback_response(prompt)

    def call_llm_with_system_prompt(self, system_prompt: str, prompt: str, max_tokens: int = 1500, max_retries: int = 3) -> str:
        """使用临时system prompt调用LLM（带重试）"""
        original = self.system_prompt
        try:
            self.system_prompt = system_prompt
            return self.call_llm(prompt, max_tokens=max_tokens, max_retries=max_retries)
        finally:
            self.system_prompt = original
    
    def _get_fallback_response(self, prompt: str) -> str:
        """当API调用失败时返回错误信息"""
        return f"❌ {self.name} API调用失败，请检查网络连接和API密钥。"

class LLMA(LLMBase):
    """世界场景描述LLM"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, LLMA_SYSTEM_PROMPT, "LLMA")
        self.world_model = None
        self.extra_rooms: List[Dict[str, Any]] = []
    
    def load_world_model(self, world_model_path: str):
        """加载世界模型"""
        try:
            with open(world_model_path, 'r', encoding='utf-8') as f:
                self.world_model = json.load(f)
            print(f"✅ {self.name} 世界模型已加载")
        except Exception as e:
            print(f"❌ {self.name} 世界模型加载失败: {str(e)}")

    def add_extra_room(self, room_path: str) -> bool:
        """按需加载其他房间（跨房间检索用）"""
        try:
            with open(room_path, 'r', encoding='utf-8') as f:
                room = json.load(f)
                self.extra_rooms.append(room)
            return True
        except Exception:
            return False
    
    def describe_scene(self) -> str:
        """描述当前真实世界场景"""
        if not self.world_model:
            return "❌ 世界模型未加载"
        
        world_model_str = json.dumps(self.world_model, ensure_ascii=False, indent=2)
        prompt = CONVERSATION_FLOW_PROMPTS["llma_initial_description"].format(
            world_model=world_model_str
        )
        
        print(f"🤖 {self.name} 正在描述世界场景...")
        response = self.call_llm(prompt)
        return response
    
    def answer_question(self, question: str, allow_cross_room: bool = False) -> str:
        """回答关于世界模型的问题"""
        if not self.world_model:
            return "❌ 世界模型未加载"
        
        world_model_str = json.dumps(self.world_model, ensure_ascii=False, indent=2)
        prompt = CONVERSATION_FLOW_PROMPTS["llma_question"].format(
            world_model=world_model_str,
            question=question,
        )
        
        print(f"🤖 {self.name} 正在回答问题...")
        response = self.call_llm(prompt)
        # 若信息不足且允许跨房间，逐个房间补充检索
        if allow_cross_room and "无法确定" in response and self.extra_rooms:
            for room in self.extra_rooms:
                room_str = json.dumps(room, ensure_ascii=False, indent=2)
                cross_prompt = CONVERSATION_FLOW_PROMPTS["llma_question"].format(
                    world_model=room_str,
                    question=question,
                )
                follow = self.call_llm(cross_prompt)
                if "无法确定" not in follow:
                    return follow
        return response

    def answer_question_structured(self, question: str, allow_cross_room: bool = False) -> Dict[str, Any]:
        """结构化回答（带不确定性与证据标注）"""
        if not self.world_model:
            return {
                "answer_text": "❌ 世界模型未加载",
                "certainty_score": 0.0,
                "involved_object_ids": [],
                "source_evidence": [],
                "insufficiency_reason": "other",
                "cross_room_used": False,
            }

        import json as _json
        import re as _re

        def _ask(world_data: Dict[str, Any]) -> Dict[str, Any]:
            world_model_str = _json.dumps(world_data, ensure_ascii=False, indent=2)
            prompt = (
                "世界模型数据：\n" + world_model_str + "\n\n问题：" + question
            )
            raw = self.call_llm_with_system_prompt(LLMA_ANALYSIS_PROMPT, prompt)
            # 提取JSON（有些模型可能包裹代码块）
            match = _re.search(r"\{[\s\S]*\}", raw)
            text = match.group(0) if match else raw
            try:
                data = _json.loads(text)
            except Exception:
                data = {
                    "answer_text": "根据提供的数据，无法确定",
                    "certainty_score": 0.0,
                    "involved_object_ids": [],
                    "source_evidence": [{"snippet": "parser_error"}],
                    "insufficiency_reason": "other",
                    "cross_room_used": False,
                }
            # 兜底字段
            data.setdefault("answer_text", "根据提供的数据，无法确定")
            data.setdefault("certainty_score", 0.0)
            data.setdefault("involved_object_ids", [])
            data.setdefault("source_evidence", [])
            data.setdefault("insufficiency_reason", None)
            data.setdefault("cross_room_used", False)
            return data

        # 先在当前房间回答
        analysis = _ask(self.world_model)
        if allow_cross_room and ("无法确定" in analysis.get("answer_text", "")) and self.extra_rooms:
            for room in self.extra_rooms:
                follow = _ask(room)
                if "无法确定" not in follow.get("answer_text", ""):
                    follow["cross_room_used"] = True
                    return follow
            # 所有房间仍不确定
            analysis["cross_room_used"] = True
        return analysis

class LLMB(LLMBase):
    """任务思考LLM"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, LLMB_SYSTEM_PROMPT, "LLMB")
        self.llma = None
        self.conversation_history = []
        self.strategy_hint = "系统化推进"
    
    def set_llma(self, llma: LLMA):
        """设置LLMA引用"""
        self.llma = llma
    
    def start_thinking(self, scene_description: str, strategy_hint: str) -> str:
        """开始思考任务"""
        prompt = CONVERSATION_FLOW_PROMPTS["llmb_initial_analysis"].format(
            world_description=scene_description,
            strategy_hint=strategy_hint,
        )
        
        print(f"🤖 {self.name} 开始思考任务...")
        response = self.call_llm(prompt)
        self.conversation_history.append(f"LLMB: {response}")
        return response
    
    def continue_thinking(self, scene_description: str, llma_answer: str, strategy_hint: str) -> str:
        """继续思考任务"""
        conversation_text = "\n".join(self.conversation_history)
        prompt = CONVERSATION_FLOW_PROMPTS["llmb_continue_analysis"].format(
            world_description=scene_description,
            conversation_history=conversation_text,
            llma_answer=llma_answer,
            strategy_hint=strategy_hint,
        )
        
        print(f"🤖 {self.name} 继续思考任务...")
        response = self.call_llm(prompt)
        self.conversation_history.append(f"LLMA: {llma_answer}")
        self.conversation_history.append(f"LLMB: {response}")
        return response
    
    def has_question(self, response: str) -> bool:
        """检查响应中是否包含问题"""
        # 首先检查是否包含任务清单（多行格式）
        lines = response.strip().split('\n')
        if len(lines) > 1:
            # 多行响应，检查是否像任务清单
            task_indicators = ["clean_", "empty_", "organize_", "move_", "pick_", "place_", "open_", "close_"]
            if any(any(indicator in line.lower() for indicator in task_indicators) for line in lines):
                return False  # 这看起来像任务清单，不是问题
        
        # 检查是否包含问题指示词
        question_indicators = ["？", "?", "请问", "我需要知道", "请告诉我", "什么", "哪里", "如何", "是否", "位置", "状态", "ID"]
        return any(indicator in response for indicator in question_indicators)
    
    def extract_question(self, response: str) -> str:
        """从响应中提取问题"""
        lines = response.split('\n')
        for line in lines:
            if any(indicator in line for indicator in ["？", "?", "请问", "我需要知道", "请告诉我"]):
                return line.strip()
        return ""
    
    def is_task_list(self, response: str) -> bool:
        """检查响应是否是任务清单"""
        lines = response.strip().split('\n')
        if len(lines) < 2:
            return False
        
        # 检查是否包含任务相关的动词和对象
        task_verbs = ["clean", "empty", "organize", "move", "pick", "place", "open", "close", "turn_on", "turn_off"]
        task_objects = ["sink", "stove", "range_hood", "trash", "floor", "knife", "cabinet", "drawer"]
        
        task_count = 0
        for line in lines:
            line_lower = line.lower().strip()
            if line_lower and not line_lower.startswith(('a:', 'b:', 'llma:', 'llmb:')):
                # 检查是否包含任务动词和对象
                has_verb = any(verb in line_lower for verb in task_verbs)
                has_object = any(obj in line_lower for obj in task_objects)
                if has_verb and has_object:
                    task_count += 1
        
        # 如果有2个或以上的任务行，认为是任务清单
        return task_count >= 2

class LLMC(LLMBase):
    """任务序列优化LLM"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, LLMC_SYSTEM_PROMPT, "LLMC")
    
    def optimize_sequence(self, task_list: str) -> str:
        """将任务列表转换为排序的可执行序列"""
        prompt = CONVERSATION_FLOW_PROMPTS["llmc_task_optimization"].format(
            task_sequence=task_list
        )
        
        print(f"🤖 {self.name} 正在优化任务序列...")
        response = self.call_llm(prompt)
        return response

class TaskExpander(LLMBase):
    """将最小化任务清单扩展为详细步骤的LLM"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, TASK_EXPANDER_SYSTEM_PROMPT, "TaskExpander")
    
    def expand(self, task_sequence: str) -> str:
        prompt = TASK_EXPANDER_PROMPT.format(task_sequence=task_sequence)
        print(f"🤖 {self.name} 正在生成详细步骤...")
        return self.call_llm(prompt)

class ThreeLLMSystemV2:
    """三个LLM协作系统 V2"""
    
    def __init__(self, api_key: str, world_model_path: str, extra_room_paths: Optional[List[str]] = None):
        self.api_key = api_key
        self.world_model_path = world_model_path
        
        # 初始化三个LLM
        self.llma = LLMA(api_key)
        self.llmb = LLMB(api_key)
        self.llmc = LLMC(api_key)
        self.expander = TaskExpander(api_key)
        
        # 设置LLMB对LLMA的引用
        self.llmb.set_llma(self.llma)
        
        # 加载世界模型
        self.llma.load_world_model(world_model_path)
        # 预加载可选的其他房间（用于跨房间检索）
        if extra_room_paths:
            for p in extra_room_paths:
                self.llma.add_extra_room(p)
        
        # MAB 平衡器（控制追问与收敛）
        self.mab = MABBalancer(
            exploration_factor=2.0,
            max_questions_per_subtask=6,
            min_reward_threshold=0.4,
            min_questions_before_stop=3,
        )

        # 证据与子任务就绪度追踪
        self.evidence_store: Dict[str, Dict[str, Any]] = {}
        self.ready_subtasks: set = set()

        print("✅ 三个LLM协作系统 V2 初始化完成")
    
    def _extract_candidate_subtasks(self, scene_description: str) -> List[str]:
        """从A的场景描述中挖掘多个候选子任务关键点（启发式）"""
        candidates: List[str] = []
        text = scene_description
        # 简单启发式：包含关键词则生成高层候选子任务标签
        if "水槽" in text or "sink_001" in text or "洗手池" in text:
            candidates.append("clean_sink")
        if "马桶" in text or "toilet_001" in text:
            candidates.append("clean_toilet")
        if "镜子" in text or "mirror_001" in text:
            candidates.append("clean_mirror")
        if "浴缸" in text or "bathtub_001" in text:
            candidates.append("clean_bathtub")
        if "地板" in text or "floor_area_002" in text or "拖把" in text or "mop_001" in text:
            candidates.append("clean_floor")
        if "灶台" in text or "stove_001" in text:
            candidates.append("clean_stove")
        if "抽油烟机" in text or "range_hood_001" in text:
            candidates.append("clean_range_hood")
        if "垃圾桶" in text or "trash_bin_001" in text:
            candidates.append("empty_trash")
        if "刀" in text or "knife_001" in text:
            candidates.append("organize_knife")
        if not candidates:
            candidates.append("general_cleanup")
        return candidates

    def _readiness_breakdown_text(self, subtask_id: str) -> str:
        """生成就绪度文本（含四元组分项）"""
        reqs = self.subtask_requirements.get(subtask_id, {})
        r = self._compute_readiness(subtask_id)
        return (
            f"{r:.2f} (对象ID:{'✓' if reqs.get('object_id_known') else '×'}, "
            f"工具/耗材:{'✓' if reqs.get('tool_or_consumable_known') else '×'}, "
            f"位置/可达:{'✓' if reqs.get('location_or_access_known') else '×'}, "
            f"状态:{'✓' if reqs.get('key_state_known') else '×'})"
        )

    def _subtask_requirements_template(self, subtask_id: str) -> Dict[str, Any]:
        """为子任务构建四元组需求模板"""
        return {
            "object_id_known": False,
            "tool_or_consumable_known": False,
            "location_or_access_known": False,
            "key_state_known": False,
            "involved_objects": set(),
            "involved_tools": set(),
        }

    def _init_subtask_requirements(self, candidate_subtasks: List[str]) -> Dict[str, Dict[str, Any]]:
        return {sid: self._subtask_requirements_template(sid) for sid in candidate_subtasks}

    def _update_evidence_from_analysis(self, analysis: Dict[str, Any], current_subtask: str, known_ids: List[str]) -> Dict[str, bool]:
        """根据结构化回答更新证据与子任务需求，返回是否新增了各类信息"""
        added = {
            "object": False,
            "tool": False,
            "location": False,
            "state": False,
        }
        answer_text = analysis.get("answer_text", "")
        involved_ids: List[str] = analysis.get("involved_object_ids", []) or []

        # 记录对象与工具（粗略：目标对象假定为第一个，其余视作潜在工具）
        for idx, oid in enumerate(involved_ids):
            if oid not in known_ids:
                continue
            prev = self.evidence_store.get(oid)
            if not prev:
                self.evidence_store[oid] = {"mentioned": True, "location": False, "state": False, "access": False}
                added["object"] = True
            # 标注可能的工具
        # 基于文本启发检测新信息
        def _contains_any(text: str, kws: List[str]) -> bool:
            return any(k in (text or "") for k in kws)

        loc_hit = _contains_any(answer_text, ["位置", "位于", "在 ", "可达", "access", "reachable"])
        state_hit = _contains_any(answer_text, ["状态", "开关", "开启", "关闭", "可用", "不可用", "on", "off"])

        if involved_ids:
            target_id = involved_ids[0]
            st = self.evidence_store.setdefault(target_id, {"mentioned": True, "location": False, "state": False, "access": False})
            if loc_hit and not st["location"]:
                st["location"] = True
                st["access"] = True
                added["location"] = True
            if state_hit and not st["state"]:
                st["state"] = True
                added["state"] = True

        # 更新子任务需求映射
        reqs = self.subtask_requirements.get(current_subtask)
        if reqs is not None:
            if involved_ids:
                if not reqs["object_id_known"]:
                    reqs["object_id_known"] = True
                    added["object"] = True or added["object"]
                reqs["involved_objects"].update(involved_ids[:1])
                if len(involved_ids) > 1:
                    reqs["tool_or_consumable_known"] = True
                    reqs["involved_tools"].update(involved_ids[1:])
                    added["tool"] = True
            if loc_hit:
                reqs["location_or_access_known"] = True
            if state_hit:
                reqs["key_state_known"] = True

        return added

    def _compute_readiness(self, subtask_id: str) -> float:
        reqs = self.subtask_requirements.get(subtask_id)
        if not reqs:
            return 0.0
        keys = ["object_id_known", "tool_or_consumable_known", "location_or_access_known", "key_state_known"]
        filled = sum(1 for k in keys if reqs.get(k))
        return filled / 4.0
    
    def _calculate_reward(self, llma_answer: str, question: str) -> float:
        """
        计算回报值，基于A的回答质量
        
        Args:
            llma_answer (str): A的回答
            question (str): B的问题
            
        Returns:
            float: 回报值 (0.0-1.0)
        """
        # 如果A回答"根据提供的数据，无法确定"，给予较低回报
        if "根据提供的数据，无法确定" in llma_answer:
            return 0.1
        
        # 如果A提供了具体信息（包含ID、状态等），给予较高回报
        if any(keyword in llma_answer for keyword in ["ID", "状态", "位置", "可用", "可达"]):
            return 0.8
        
        # 如果A提供了有用信息但不完整，给予中等回报
        if len(llma_answer.strip()) > 10:  # 有实质性内容
            return 0.5
        
        # 其他情况给予较低回报
        return 0.2

    def _build_strategy_hint(self, base_hint: str, subtask_id: str, insuff_reason: Optional[str]) -> str:
        reqs = self.subtask_requirements.get(subtask_id, {})
        deficits = []
        if not reqs.get("object_id_known"): deficits.append("对象ID")
        if not reqs.get("tool_or_consumable_known"): deficits.append("工具/耗材ID")
        if not reqs.get("location_or_access_known"): deficits.append("位置/可达性")
        if not reqs.get("key_state_known"): deficits.append("关键状态")
        reason_text = f"；上轮不足原因：{insuff_reason}" if insuff_reason else ""
        return base_hint + f"；当前子任务缺口：{', '.join(deficits) if deficits else '无'}{reason_text}。请更换角度针对缺口提出一个事实性问题。"

    def _strip_ready_tags(self, task_list: str) -> str:
        import re
        return re.sub(r"\s*\[ready=\s*([0-9]*\.?[0-9]+)\]\s*$", "", task_list, flags=re.MULTILINE)

    def _get_known_object_ids(self) -> List[str]:
        """从已加载的世界模型中提取 objects 列表内的已知对象ID"""
        try:
            objects = self.llma.world_model.get("objects", []) if self.llma.world_model else []
            return [obj.get("id") for obj in objects if isinstance(obj, dict) and obj.get("id")]
        except Exception:
            return []
    
    def _parse_task_analysis(self, analysis_text: str, scene_description: str = "") -> List[str]:
        """解析B的任务分析，提取任务列表"""
        task_list = []
        lines = analysis_text.split('\n')
        in_task_section = False
        
        # 如果直接是问题（没有结构化分析），则从候选子任务中生成默认任务列表
        if not any('【场景任务分析】' in line for line in lines):
            # 从候选子任务生成默认任务列表
            candidates = self._extract_candidate_subtasks(scene_description)
            if candidates:
                return candidates[:3]  # 返回前3个候选任务
            else:
                return ["clean sink", "clean toilet", "clean floor"]  # 默认任务
        
        for line in lines:
            line = line.strip()
            if '【场景任务分析】' in line:
                in_task_section = True
                continue
            elif '【开始追问】' in line:
                break
            elif in_task_section and line and (line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or 
                                               line.startswith(('1、', '2、', '3、', '4、', '5、', '6、', '7、', '8、', '9、'))):
                # 提取任务描述，去掉序号
                task_desc = line.split('.', 1)[-1].split('：', 1)[-1].strip()
                if task_desc:
                    task_list.append(task_desc)
        
        return task_list
    
    def _extract_first_question(self, analysis_text: str) -> str:
        """从B的初始分析中提取第一个问题"""
        lines = analysis_text.split('\n')
        in_question_section = False
        
        for line in lines:
            line = line.strip()
            if '【开始追问】' in line:
                in_question_section = True
                continue
            elif in_question_section and line and ('针对' in line or '问题：' in line or '?' in line or '？' in line):
                # 提取问题，去掉前缀
                question = line.split('：', 1)[-1].strip()
                if question:
                    return question
        
        return ""
    
    def _map_task_to_subtask(self, task: str) -> str:
        """将B识别的任务映射到对应的子任务ID"""
        task_lower = task.lower()
        if '清洁' in task or 'clean' in task_lower:
            if '地板' in task or 'floor' in task_lower:
                return 'clean_floor'
            elif '马桶' in task or 'toilet' in task_lower:
                return 'clean_toilet'
            elif '洗手池' in task or 'sink' in task_lower:
                return 'clean_sink'
            elif '浴缸' in task or 'bathtub' in task_lower:
                return 'clean_bathtub'
        elif '整理' in task or 'organize' in task_lower:
            return 'organize_items'
        elif '清空' in task or 'empty' in task_lower:
            return 'empty_trash'
        return None
    
    def _is_task_list(self, text: str) -> bool:
        """检查文本是否是任务清单"""
        lines = text.strip().split('\n')
        task_indicators = ['clean ', 'empty ', 'organize ', 'move ', 'place ']
        for line in lines:
            line = line.strip()
            if any(indicator in line.lower() for indicator in task_indicators):
                return True
        return False

    def _normalize_question(self, q: str) -> str:
        """对问句做简单标准化用于去重"""
        if not q:
            return ""
        q = q.strip().lower()
        for ch in ["？", "?", "！", "!", "。", ",", "，", "：", ":"]:
            q = q.replace(ch, " ")
        q = " ".join(q.split())
        return q

    def _extract_ids_from_text(self, text: str) -> List[str]:
        """从文本中提取类似 xxx_001 的ID标记"""
        import re
        return re.findall(r"[a-zA-Z_]+_\d{3}", text or "")

    def execute_planning(self) -> Dict[str, str]:
        """执行完整的规划流程"""
        print(f"\n🚀 开始执行规划流程")
        print("="*60)
        
        results = {}
        
        # 步骤1: LLMA描述场景
        print("\n📊 步骤1: LLMA描述场景")
        scene_description = self.llma.describe_scene()
        results["scene_description"] = scene_description
        print(f"A: {scene_description}")
        
        # 步骤2: LLMB开始思考任务（带策略提示）
        print("\n📋 步骤2: LLMB开始思考任务")
        candidate_subtasks = self._extract_candidate_subtasks(scene_description)
        # 初始化子任务需求
        self.subtask_requirements = self._init_subtask_requirements(candidate_subtasks)
        known_ids = self._get_known_object_ids()
        strategy_hint = (
            "系统化推进，逐步追问且每轮仅一个问题；候选子任务："
            + ", ".join(candidate_subtasks)
            + f"。当前关注：{candidate_subtasks[0]}。仅围绕该子任务提出事实性问题（工具/耗材ID、对象ID、位置、状态、可达性、开关等）。"
            + "已知对象ID仅限于："
            + ", ".join(known_ids[:50])
            + (" 等" if len(known_ids) > 50 else "")
            + "。不要询问未在已知对象ID中的ID。若上一问被判定为‘根据提供的数据，无法确定’，必须换问题，不要重复或同义改写。"
        )
        llmb_response = self.llmb.start_thinking(scene_description, strategy_hint)
        print(f"B: {llmb_response}")
        # 打印初始就绪度
        print("\n📈 初始就绪度：")
        for sid in candidate_subtasks:
            print(f"  - {sid}: {self._readiness_breakdown_text(sid)}")
        
        # 步骤3: LLMB和LLMA对话循环（加入MAB gating + 任务队列）
        print("\n💬 步骤3: LLMB和LLMA对话交流")
        
        # 让B先全局分析场景任务
        print("🔍 B正在全局分析场景任务...")
        initial_analysis = self.llmb.start_thinking(scene_description, "开始全局分析")
        print(f"B: {initial_analysis}")
        
        # 解析B的任务分析，提取任务列表
        task_list = self._parse_task_analysis(initial_analysis, scene_description)
        print(f"\n📋 B识别的任务列表：")
        for i, task in enumerate(task_list, 1):
            print(f"  {i}. {task}")
        
        # 初始化任务队列状态
        current_task_index = 0
        completed_tasks = set()
        paused_tasks = set()  # 被MAB暂存的任务
        
        max_rounds = 15  # 防止无限循环，提高追问上限
        round_count = 0
        asked_questions = set()
        known_ids = self._get_known_object_ids()
        conversation_history = ""
        
        # 从初始分析中提取第一个问题
        first_question = self._extract_first_question(initial_analysis)
        llma_answer = ""  # 初始化llma_answer变量
        if first_question:
            print(f"B: {first_question}")
            llma_answer = self.llma.answer_question(first_question)
            print(f"A: {llma_answer}")
            conversation_history += f"B: {first_question}\nA: {llma_answer}\n"
            asked_questions.add(self._normalize_question(first_question))
            round_count += 1
        
        while round_count < max_rounds:
            round_count += 1
            print(f"\n--- 对话轮次 {round_count} ---")
            
            # 检查是否还有任务需要处理
            if current_task_index >= len(task_list):
                print("✅ 所有任务已完成或暂存")
                break
            
            current_task = task_list[current_task_index]
            print(f"🎯 当前任务: {current_task}")
            
            # 构建策略提示
            strategy_hint = f"当前专注于任务: {current_task}"
            
            # B提问
            question = self.llmb.continue_thinking(scene_description, llma_answer, strategy_hint)
            
            # 检查是否输出了任务清单
            if self._is_task_list(question):
                print(f"✅ B输出了任务清单：\n{question}")
                task_sequence = question
                break
            
            # 去重检查
            normalized_q = self._normalize_question(question)
            if normalized_q in asked_questions:
                print(f"⚠️ 问题重复，切换到下一个任务")
                current_task_index += 1
                continue
            
            asked_questions.add(normalized_q)
            print(f"B: {question}")
            
            # A回答
            llma_answer = self.llma.answer_question(question)
            print(f"A: {llma_answer}")
            
            # 更新对话历史
            conversation_history += f"B: {question}\nA: {llma_answer}\n"
            
            # MAB决策 - 基于当前任务的就绪度
            current_subtask = self._map_task_to_subtask(current_task)
            if current_subtask:
                # 计算回报：基于A的回答质量
                reward = self._calculate_reward(llma_answer, question)
                self.mab.update_reward(current_subtask, reward)
                print(f"💰 回报[{current_subtask}]: {reward:.2f}")
                
                readiness = self._compute_readiness(current_subtask)
                print(f"📈 就绪度[{current_subtask}]: {self._readiness_breakdown_text(current_subtask)}")
                
                if self.mab.should_continue_questioning(current_subtask, round_count):
                    print(f"✅ MAB决定继续当前任务")
                    # 继续当前任务
                else:
                    print(f"⏹️ MAB决定暂存当前任务，切换到下一个")
                    paused_tasks.add(current_task)
                    current_task_index += 1
                    # 给B一个提示，说明任务已切换
                    if current_task_index < len(task_list):
                        next_task = task_list[current_task_index]
                        strategy_hint = f"任务已切换，当前专注于任务: {next_task}"
                        # 让B知道任务切换了
                        self.llmb.continue_thinking(scene_description, llma_answer, strategy_hint)
            else:
                # 如果没有对应的子任务，继续当前任务
                print(f"✅ 继续当前任务")
        
        # 如果没有生成任务清单，使用默认的
        if 'task_sequence' not in locals():
            task_sequence = "clean floor - mop_001"  # 默认任务
        
        results["task_list"] = task_sequence
        results["conversation_rounds"] = round_count
        print(f"{task_sequence}")
        
        # 打印任务状态总览
        print("\n📊 任务状态总览：")
        print(f"  - 已完成任务: {len(completed_tasks)}")
        print(f"  - 已暂存任务: {len(paused_tasks)}")
        print(f"  - 剩余任务: {len(task_list) - current_task_index}")
        
        # 步骤4: LLMC优化任务序列
        print("\n🔧 步骤4: LLMC优化任务序列")
        # 去除任务行中的就绪度标注
        cleaned_task_list = self._strip_ready_tags(task_sequence)
        optimized_sequence = self.llmc.optimize_sequence(cleaned_task_list)
        results["optimized_sequence"] = optimized_sequence
        print(f"{optimized_sequence}")
        
        # 步骤5: 扩展详细步骤
        print("\n🧩 步骤5: 详细步骤扩展")
        detailed_steps = self.expander.expand(optimized_sequence)
        results["detailed_steps"] = detailed_steps
        print(f"{detailed_steps}")

        return results
    
    def save_results(self, results: Dict[str, str], output_path: str = None):
        """保存结果"""
        if not output_path:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_path = f"three_llm_v2_results_{timestamp}.json"
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {output_path}")
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 三个LLM协作的具身任务规划系统 V2")
    print("="*50)
    
    try:
        # 初始化系统 - 启用跨房间模式
        extra_room_paths = [
            "maps/rooms/living_room.json",
            "maps/rooms/bedroom.json", 
            "maps/rooms/kitchen.json"
        ]
        system = ThreeLLMSystemV2(
            DASHSCOPE_API_KEY, 
            "maps/rooms/bathroom.json",
            extra_room_paths=extra_room_paths
        )
        
        # 执行规划
        results = system.execute_planning()
        
        # 保存结果
        system.save_results(results)
        
        print("\n🎉 规划完成！")
        print("\n💡 系统特性:")
        print("   ✅ LLMA: 直接描述真实世界场景")
        print("   ✅ LLMB: 自己思考任务并向A追问具体信息")
        print("   ✅ LLMC: 只负责任务序列优化，不输出建议")
        print("   ✅ 无用户指令，A直接描述场景")
        print("   🌐 跨房间模式: 自动检索厨房、客厅、卧室、浴室信息")
        
    except Exception as e:
        print(f"❌ 系统执行失败: {str(e)}")

if __name__ == "__main__":
    main()
