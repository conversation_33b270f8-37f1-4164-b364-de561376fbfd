# 具身任务规划系统 v3.0 - 真实LLM API版本

## 🎯 系统概述

这是一个完全基于真实LLM API调用的具身任务规划系统，没有任何预设回答或硬编码逻辑。系统通过真实的LLM API动态生成任务分解、规划和执行步骤。

## ✨ 核心特性

- **🔌 真实API调用**: 所有LLM对话都通过真实的API调用生成
- **🚫 无预设回答**: 完全基于世界模型的动态查询和规划
- **🌐 多提供商支持**: 支持OpenAI、Anthropic、Google等主流LLM提供商
- **⚙️ 可配置参数**: 灵活配置模型、温度、最大token数等参数
- **📊 完整日志**: 记录所有查询、响应和规划过程
- **🔒 安全配置**: 支持环境变量和配置文件管理API密钥

## 🏗️ 系统架构

```
用户指令 → 任务规划器(LLM) → 世界模型查询引擎(LLM) → 任务细化 → 执行计划
    ↓              ↓                    ↓              ↓         ↓
高层指令      任务分解和规划        动态查询世界模型    基于查询结果    具体执行步骤
```

### 核心组件

1. **RealLLMClient**: 真实的LLM API客户端
2. **WorldModelQueryEngine**: 通过LLM查询世界模型
3. **TaskPlanner**: 通过LLM进行任务规划
4. **EmbodiedTaskPlannerV3**: 主系统协调器

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- 有效的LLM API密钥
- 网络连接

### 2. 安装依赖

```bash
pip install requests
```

### 3. 配置API密钥

#### 方法1: 使用配置文件

```bash
# 运行配置管理器创建模板
python config.py

# 编辑配置文件
cp llm_config_template.json llm_config.json
# 在 llm_config.json 中填入真实的API密钥
```

#### 方法2: 设置环境变量

```bash
# Windows
set OPENAI_API_KEY=your_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_api_key_here
```

### 4. 运行系统

```bash
python embodied_task_planner_v3_real_api.py
```

## 📁 文件结构

```
├── embodied_task_planner_v3_real_api.py  # 主系统文件
├── config.py                              # 配置管理器
├── README.md                              # 本文档
├── llm_config.json                        # API配置文件(用户创建)
├── llm_config_template.json               # 配置模板
├── maps/example_world_model.json          # 示例世界模型
└── embodied_planning_report_v3_*.json    # 规划报告(运行时生成)
```

## ⚙️ 配置说明

### LLM提供商配置

系统支持以下LLM提供商：

- **OpenAI**: GPT-4, GPT-3.5-turbo
- **Anthropic**: Claude-3系列
- **Google**: Gemini Pro
- **本地API**: 自定义API端点

### 配置参数

```python
@dataclass
class LLMConfig:
    provider: LLMProvider          # 提供商
    api_key: str                   # API密钥
    base_url: str                  # API基础URL
    model: str                     # 模型名称
    temperature: float = 0.7       # 温度参数
    max_tokens: int = 2000         # 最大token数
```

## 🔧 使用方法

### 基本使用

```python
from embodied_task_planner_v3_real_api import EmbodiedTaskPlannerV3, LLMConfig, LLMProvider

# 配置LLM
llm_config = LLMConfig(
    provider=LLMProvider.OPENAI,
    api_key="your_api_key",
    base_url="https://api.openai.com",
    model="gpt-4"
)

# 初始化系统
planner = EmbodiedTaskPlannerV3("maps/world_model.json", llm_config)

# 执行规划
instruction = "打扫浴室"
task_plan = planner.execute_planning_session(instruction)

# 查看结果
planner.print_query_log()
planner.save_planning_report()
```

### 自定义世界模型

世界模型是一个JSON文件，包含以下结构：

```json
{
  "objects": [
    {
      "id": "object_id",
      "class_name": "object_type",
      "position": {"x": 0, "y": 0, "z": 0},
      "state": "clean/dirty/moderate",
      "properties": {},
      "is_movable": false,
      "is_interactable": true
    }
  ],
  "areas": [
    {
      "id": "area_id",
      "name": "区域名称",
      "type": "区域类型",
      "boundary": {"x_min": 0, "x_max": 10, "y_min": 0, "y_max": 10}
    }
  ],
  "relationships": [
    {
      "subject_id": "object1",
      "object_id": "object2",
      "relationship_type": "关系类型",
      "description": "关系描述"
    }
  ]
}
```

## 📊 输出示例

### 规划结果

```
🎯 具身任务规划结果 (v3.0 - 真实LLM API)
================================================================================

📊 规划统计:
   • 高层任务数量: 4
   • 总执行步骤: 12
   • 预估执行时间: 45 分钟

🔧 资源需求:
   • 需要工具: ['sponge_001', 'dish_soap_001', 'vacuum_cleaner_001']
   • 清洁区域: ['sink_001', 'toilet_001', 'floor_area_001']

📋 详细任务:
    1. 清洁水槽 (sink_001)
       细化步骤: 4 个
    2. 清洁马桶 (toilet_001)
       细化步骤: 3 个
    3. 清洁地板 (floor_area_001)
       细化步骤: 3 个
    4. 清洁台面 (counter_area_001)
       细化步骤: 2 个
```

### 查询日志

```
💬 查询-响应日志
================================================================================

 1. 查询ID: query_001
    ❓ 查询: What is the current state and properties of sink_001?
    📅 时间: 2024-01-15T10:30:00

 2. 查询ID: query_002
    ❓ 查询: What tools are available for clean sink_001?
    📅 时间: 2024-01-15T10:30:05
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查API密钥是否正确
   - 确认API密钥有足够的配额
   - 验证网络连接

2. **LLM响应解析失败**
   - 检查LLM是否返回有效JSON
   - 调整系统提示词
   - 检查模型参数设置

3. **世界模型加载失败**
   - 确认文件路径正确
   - 检查JSON格式是否有效
   - 验证文件编码

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔒 安全注意事项

1. **API密钥保护**
   - 不要在代码中硬编码API密钥
   - 使用环境变量或配置文件
   - 不要将包含API密钥的文件提交到版本控制

2. **网络安全**
   - 确保在安全的网络环境中使用
   - 考虑使用VPN或代理

3. **数据隐私**
   - 注意LLM提供商的数据使用政策
   - 避免发送敏感信息

## 🚧 开发计划

- [ ] 支持更多LLM提供商
- [ ] 添加任务执行监控
- [ ] 实现动态世界模型更新
- [ ] 添加多语言支持
- [ ] 开发Web界面

## 📝 许可证

本项目采用MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 使用本系统需要有效的LLM API密钥，并会产生相应的API调用费用。请确保了解相关费用结构。
