# 增强版具身任务规划系统

## 🚀 项目概述

这是一个基于LLM智能体协作的先进具身任务规划框架，专门设计用于处理复杂、模糊的具身智能体指令，并基于多模态感知数据生成详细、逻辑严谨且可执行的任务序列。

## 🎯 核心创新点

### 1. 结构化世界模型
- **多模态感知集成**: 整合视觉、听觉、空间等多模态信息
- **语义化表示**: 将物理世界抽象为结构化的JSON模型
- **动态更新**: 支持实时世界状态更新和验证

### 2. 多臂老虎机（MAB）平衡模块
- **智能追问策略**: 平衡探索（获取新信息）和利用（基于现有信息执行）
- **UCB1算法**: 实现最优的探索-利用平衡
- **自适应决策**: 根据信息回报动态调整追问策略

## 🏗️ 系统架构

### 三个核心LLM智能体

#### 🤖 LLM A (真实世界代理)
- **职责**: 唯一的事实来源，被动信息库
- **输入**: 结构化世界模型JSON
- **输出**: 基于世界模型的事实性回答
- **特点**: 不进行主动规划，仅提供事实查询服务

#### 🧠 LLM B (任务生成与分解智能体)
- **职责**: 任务分析、分解和细化
- **核心功能**: 内置MAB平衡模块
- **工作流程**:
  1. 分析用户指令，识别子任务
  2. 使用MAB策略决定追问策略
  3. 生成具体的可执行任务序列

#### ⚙️ LLM C (任务排序智能体)
- **职责**: 任务序列优化和排序
- **优化策略**: 依赖关系、路径优化、并行执行识别
- **输出**: 逻辑最优的任务执行顺序

## 📊 世界模型结构

```json
{
  "scene_id": "string",
  "scene_name": "string",
  "objects": [
    {
      "id": "string",
      "class_name": "string",
      "position": {"x": "float", "y": "float", "z": "float"},
      "properties": [{"type": "string", "value": "string"}],
      "state": "string",
      "is_movable": "boolean",
      "is_interactable": "boolean",
      "associated_tools": ["string"]
    }
  ],
  "areas": [
    {
      "id": "string",
      "name": "string",
      "boundary": {"min_x": "float", "max_x": "float", ...}
    }
  ],
  "sounds": [
    {
      "id": "string",
      "sound_class": "string",
      "location": {"x": "float", "y": "float", "z": "float"}
    }
  ],
  "relationships": [
    {
      "subject_id": "string",
      "relationship_type": "string",
      "object_id": "string"
    }
  ]
}
```

## 🔄 核心交互流程

### 1. 系统启动
- 加载指定的世界模型
- LLM A获得世界模型访问权限

### 2. 指令接收与分析
- LLM B接收用户高层指令
- 进行初步分析，识别潜在子任务

### 3. 智能追问阶段
- LLM B调用MAB模块生成追问
- 向LLM A发起事实查询
- 根据回答更新MAB回报

### 4. 任务分解与细化
- 基于获得的信息分解任务
- 生成具体的可执行动作序列
- 每个任务包含明确的动作动词和目标ID

### 5. 序列优化
- LLM C接收任务列表进行优化
- 考虑依赖关系、路径效率等因素
- 生成最优执行顺序

### 6. 逻辑验证
- 验证任务序列的逻辑合理性
- 检查依赖关系、资源冲突等

## 📁 文件结构

```
├── embodied_planner.py      # 主系统文件
├── llm_agents.py           # 三个LLM智能体实现
├── mab.py                  # 多臂老虎机平衡模块
├── world.py                # 世界模型处理模块
├── task_planner.py         # 任务规划和排序模块
├── maps/example_world_model.json # 示例世界模型
├── config.py               # 配置文件
├── requirements.txt        # 依赖包列表
└── README_EMBODIED_PLANNER.md # 本说明文档
```

## 🛠️ 安装与配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
在 `config.py` 中设置你的DashScope API密钥：
```python
DASHSCOPE_API_KEY = "your_api_key_here"
```

### 3. 准备世界模型
创建或使用现有的世界模型JSON文件，或使用系统提供的示例文件。

## 🚀 使用方法

### 基本使用
```python
from embodied_planner import EmbodiedTaskPlanner
from config import DASHSCOPE_API_KEY

# 初始化系统
planner = EmbodiedTaskPlanner(DASHSCOPE_API_KEY)

# 加载世界模型
planner.load_world_model("example_world_model.json")

# 执行任务规划
user_instruction = "帮我打扫浴室"
report = planner.plan_tasks(user_instruction)

# 显示结果摘要
planner.print_summary(report)

# 保存详细报告
planner.save_report(report, "task_planning_report.json")
```

### 高级配置
```python
# 自定义MAB参数
from mab import MABBalancer

mab = MABBalancer(
    exploration_factor=2.5,           # 探索因子
    max_questions_per_subtask=3,      # 最大追问次数
    min_reward_threshold=0.4          # 最小回报阈值
)

# 自定义任务规划器
from task_planner import TaskPlanner

planner = TaskPlanner()
planner.validation_rules["circular_dependency_check"] = False
planner.optimization_strategies["parallel_execution"] = False
```

## 📊 输出报告结构

系统生成的执行报告包含以下关键信息：

### 1. 世界模型摘要
- 场景信息、对象数量、区域数量等

### 2. 追问-回答交互记录
- 每次追问和回答的详细内容
- 交互轮次和结果

### 3. MAB决策过程日志
- 每次决策的原因和结果
- 回报更新记录

### 4. 任务分解列表
- 每个任务的详细信息
- 动作、目标、依赖关系等

### 5. 最终任务执行顺序
- 优化后的任务序列
- 执行顺序和预估时间

### 6. 逻辑验证结果
- 验证通过状态
- 错误和警告信息

## 🎯 应用场景

### 1. 家庭服务机器人
- 清洁任务规划
- 物品整理和搬运
- 环境监控和维护

### 2. 工业自动化
- 生产线任务调度
- 设备维护规划
- 质量控制流程

### 3. 医疗护理
- 病房清洁规划
- 医疗器械管理
- 患者护理流程

### 4. 仓储物流
- 货物搬运规划
- 库存管理任务
- 路径优化

## 🔧 自定义与扩展

### 1. 添加新的动作类型
在 `task_planner.py` 中的 `_get_base_time_for_action` 方法添加新动作：
```python
base_times = {
    "move_to": 30.0,
    "pick_up": 5.0,
    "your_new_action": 15.0  # 添加新动作
}
```

### 2. 扩展世界模型
在 `world_model.py` 中添加新的验证规则和查询方法。

### 3. 自定义MAB策略
继承 `MABBalancer` 类，实现自定义的探索-利用平衡策略。

## 📈 性能优化

### 1. 批量处理
- 支持批量任务规划
- 并行处理多个子任务

### 2. 缓存机制
- 世界模型查询结果缓存
- MAB决策历史缓存

### 3. 异步处理
- 支持异步任务规划
- 非阻塞式API调用

## 🧪 测试与验证

### 运行测试
```bash
# 测试世界模型模块
python world.py

# 测试MAB平衡器
python mab_balancer.py

# 测试任务规划器
python task_planner.py

# 测试完整系统
python embodied_planner.py
```

### 验证示例
系统包含完整的示例世界模型和测试用例，可以验证：
- 世界模型加载和验证
- MAB决策逻辑
- 任务分解和优化
- 完整的任务规划流程

## 🤝 贡献指南

欢迎贡献代码和改进建议！

### 贡献方式
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

### 代码规范
- 遵循PEP 8编码规范
- 添加适当的注释和文档
- 包含单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 参与讨论

## 🔮 未来规划

### 短期目标
- 支持更多世界模型格式
- 优化MAB算法性能
- 增加更多任务类型

### 长期目标
- 集成实时感知系统
- 支持多机器人协作
- 实现自适应学习能力

---

**注意**: 本系统需要有效的DashScope API密钥才能正常运行。请确保你有足够的API配额来支持系统的运行。



