# 真实API调用的主动发现具身任务规划系统 - 使用说明

## 🎯 系统概述

这是一个**真实API调用**的主动发现具身任务规划系统，LLM A和LLM B通过真实的DashScope API进行智能对话和探索，而不是预设的问答。

### 🔑 核心特性

- **真实API调用**: 使用DashScope API进行真实的LLM对话
- **主动发现过程**: LLM A主动观察环境，LLM B主动提问探索
- **智能对话**: 基于API响应的动态问答，非预设问题
- **自然语言输出**: 所有对话都使用自然语言
- **JSON任务计划**: 最终输出结构化的任务执行计划

## 🚀 快速开始

### 1. 环境准备

#### 安装依赖
```bash
pip install requests
```

#### 获取DashScope API密钥
1. 访问 [DashScope官网](https://dashscope.aliyun.com/)
2. 注册账号并登录
3. 创建API密钥
4. 复制API密钥（格式：`sk-xxxxxxxxxxxxxxxxxxxxxxxx`）

### 2. 配置API密钥

#### 方式1: 环境变量（推荐）
```bash
# Windows PowerShell
$env:DASHSCOPE_API_KEY="sk-your-api-key-here"

# Windows CMD
set DASHSCOPE_API_KEY=sk-your-api-key-here

# Linux/Mac
export DASHSCOPE_API_KEY="sk-your-api-key-here"
```

#### 方式2: 配置文件
```bash
# 运行配置工具创建模板
python api.py

# 复制模板并编辑
cp .env.template .env
# 编辑 .env 文件，填入您的API密钥
```

#### 方式3: 代码中传入
```python
from real_api_discovery_planner import RealAPIDiscoveryPlanner

# 直接传入API密钥
planner = RealAPIDiscoveryPlanner(api_key="sk-your-api-key-here")
```

### 3. 运行系统

```bash
# 运行真实API调用的主动发现系统
python real_api_discovery_planner.py
```

## 🔧 系统架构

### 1. 真实API观察者智能体 (LLM A)
- **功能**: 通过API主动观察环境并生成描述
- **API调用**: 调用DashScope API生成环境观察
- **特点**: 基于世界模型数据，生成智能化的环境描述

### 2. 真实API探索者智能体 (LLM B)
- **功能**: 通过API主动生成探索问题
- **API调用**: 动态生成问题，判断是否继续探索
- **特点**: 基于上下文智能生成问题，避免重复

### 3. API配置管理
- **配置验证**: 自动验证API密钥格式
- **多环境支持**: 支持多种环境变量名称
- **错误处理**: 优雅的配置错误处理

## 📋 工作流程

### 阶段1: API环境观察
```
🔍 LLM A 通过API主动观察环境...
✅ LLM A 完成环境观察
📝 观察描述: [通过API生成的智能环境描述]
```

### 阶段2: API主动探索
```
🔍 LLM B 通过API开始主动探索...

📝 探索轮次 1:
❓ LLM B 主动提问: [通过API动态生成的问题]
💬 LLM A 回答: [通过API生成的智能回答]

📝 探索轮次 2:
❓ LLM B 主动提问: [基于上下文的新问题]
💬 LLM A 回答: [基于事实的智能回答]
...
```

### 阶段3: API任务计划生成
```
⚙️ 通过API基于探索结果生成任务计划...
✅ 任务计划生成完成

📋 生成的任务执行序列 (通过API生成的具体任务):
   1. [API生成的智能任务1]
   2. [API生成的智能任务2]
   ...
```

## 🔑 API配置详解

### DashScope API参数
```python
{
    "model": "qwen-max",           # 使用通义千问Max模型
    "temperature": 0.7,            # 创造性控制（0.0-1.0）
    "max_tokens": 1000,           # 最大输出长度
    "top_p": 0.8                  # 核采样参数
}
```

### 支持的模型
- `qwen-max`: 通义千问Max（推荐）
- `qwen-plus`: 通义千问Plus
- `qwen-turbo`: 通义千问Turbo

### 环境变量支持
```bash
DASHSCOPE_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxx  # 主要
DASH_SCOPE_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxx  # 备用1
ALIBABA_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxx     # 备用2
```

## 📁 输出文件

### 1. 真实API调用执行报告
- **文件名**: `real_api_discovery_report.json`
- **内容**: 完整的API调用过程记录
- **包含**:
  - 初始观察结果（API生成）
  - 主动探索过程（API问答）
  - 最终任务计划（API生成）
  - 性能指标和对话记录

### 2. 报告结构示例
```json
{
  "execution_timestamp": "2025-09-02T11:30:00.000000",
  "system_type": "real_api_active_discovery",
  "system_status": "completed",
  
  "initial_observation": "[API生成的环境观察描述]",
  
  "exploration_results": {
    "questions": [
      {
        "round": 1,
        "question": "[API动态生成的问题]",
        "context": "[当前上下文]"
      }
    ],
    "answers": [
      {
        "round": 1,
        "answer": "[API生成的智能回答]",
        "question": "[对应问题]"
      }
    ],
    "discovery_rounds": 6,
    "exploration_summary": "完成6轮主动探索，获得6个关键信息"
  },
  
  "task_plan": {
    "plan_summary": "[API生成的任务计划摘要]",
    "tasks": [...],
    "total_estimated_time": "[API估算的总时间]",
    "quality_standards": [...]
  },
  
  "performance_metrics": {...},
  "conversation_log": [...]
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. API密钥错误
```
❌ 配置错误: DashScope API密钥未设置！
💡 请设置环境变量 DASHSCOPE_API_KEY 或传入 api_key 参数
```
**解决方案**: 检查API密钥是否正确设置，格式应为 `sk-xxxxxxxxxxxxxxxxxxxxxxxx`

#### 2. API调用失败
```
⚠️ API调用失败，使用备用观察: API请求失败: 401 Unauthorized
```
**解决方案**: 
- 检查API密钥是否有效
- 确认账户余额充足
- 检查API调用频率限制

#### 3. 网络连接问题
```
⚠️ API调用失败，使用备用回答: API请求失败: Connection timeout
```
**解决方案**:
- 检查网络连接
- 确认防火墙设置
- 尝试使用代理

### 备用机制

系统设计了完善的备用机制：
- **备用观察**: 当API调用失败时，使用预设的环境描述
- **备用问题**: 当API调用失败时，使用预设的问题列表
- **备用任务计划**: 当API调用失败时，使用预设的任务计划

## 🔧 高级配置

### 自定义API参数
```python
# 修改模型参数
payload = {
    "model": "qwen-plus",        # 使用不同模型
    "parameters": {
        "temperature": 0.5,      # 降低创造性
        "max_tokens": 500,       # 减少输出长度
        "top_p": 0.7            # 调整采样参数
    }
}
```

### 自定义探索策略
```python
class CustomExplorerAgent(RealAPIExplorerAgent):
    def generate_exploration_question(self, context, exploration_results):
        # 自定义问题生成逻辑
        custom_prompt = f"""
        基于以下上下文，生成一个探索性问题：
        {context}
        
        要求：
        1. 问题要具体、有针对性
        2. 避免重复已探索的内容
        3. 有助于制定执行计划
        """
        
        return self._call_api(custom_prompt)
```

## 📊 性能优化

### 1. API调用优化
- **批量处理**: 减少API调用次数
- **缓存机制**: 缓存常用响应
- **错误重试**: 智能重试机制

### 2. 响应时间优化
- **并行处理**: 同时处理多个API调用
- **异步调用**: 使用异步API调用
- **本地缓存**: 缓存世界模型数据

### 3. 成本优化
- **模型选择**: 根据需求选择合适的模型
- **Token优化**: 优化提示词长度
- **调用频率**: 控制API调用频率

## 🎯 使用场景

### 1. 智能清洁规划
- 环境状态自动识别
- 清洁任务智能规划
- 执行顺序优化

### 2. 工业任务规划
- 生产线状态监控
- 维护任务调度
- 质量控制规划

### 3. 服务机器人
- 环境感知和理解
- 任务分解和规划
- 执行监控和调整

## 🔮 未来扩展

### 1. 多模态支持
- 图像识别和描述
- 语音交互和指令
- 传感器数据融合

### 2. 学习能力
- 历史数据学习
- 任务执行反馈
- 策略自适应优化

### 3. 分布式协作
- 多智能体协作
- 任务分配和协调
- 冲突解决和协商

## 📞 技术支持

### 获取帮助
- **文档**: 查看本文档和相关代码注释
- **配置工具**: 运行 `python api_config.py` 检查配置
- **错误日志**: 查看控制台输出的详细错误信息

### 常见配置检查
```bash
# 检查配置
python api_config.py

# 测试API连接
python -c "
from api import DashScopeConfig
config = DashScopeConfig()
print('配置有效:', config.validate_config())
"
```

## 🎉 总结

真实API调用的主动发现具身任务规划系统提供了：

1. **✅ 真实API调用**: 使用DashScope API进行智能对话
2. **✅ 主动发现过程**: LLM A和LLM B自主探索和规划
3. **✅ 智能对话**: 基于上下文的动态问答
4. **✅ 自然语言输出**: 流畅的自然语言交互
5. **✅ JSON任务计划**: 结构化的执行计划输出
6. **✅ 完善的备用机制**: 确保系统稳定运行

现在您可以体验真正的AI智能对话和任务规划了！



